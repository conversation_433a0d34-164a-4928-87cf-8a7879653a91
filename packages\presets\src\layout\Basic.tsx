import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useContext, useMemo } from "react";

import { AgentList } from "@/components";
import { AgentChatContext, Conversation, useAgentConfigs, useNavigate, useSubscribeCommand } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import { PresetsCommand } from "../command";
import LogoHeader from "./LogoHeader";

interface DefaultLayoutProps {
  sidebar?: {
    agents: React.ReactNode;
    conversations: React.ReactNode;
  };
  main?: React.ReactNode;
}

export const DefaultAgentLayoutContext = React.createContext({
  sideBarOpen: true,
});

const DefaultBasicLayout: React.FC<DefaultLayoutProps> = (props) => {
  const { sidebar, main } = props;
  const agentConfigs = useAgentConfigs();
  const [sideBarOpen, setSideBarOpen] = React.useState(true);
  const { mode } = useContext(AgentChatContext);
  const navigate = useNavigate();

  useSubscribeCommand(PresetsCommand.CloseSideBar, () => {
    setSideBarOpen(false);
  });

  useSubscribeCommand(PresetsCommand.OpenSideBar, () => {
    setSideBarOpen(true);
  });

  const height = useMemo(() => {
    if (agentConfigs.length > 9) {
      return `calc(100vh - 425px)`;
    } else {
      const height = 36 * agentConfigs.length;
      return `calc(100vh - 98px - ${height}px)`;
    }
  }, [agentConfigs.length]);

  const agents = useMemo(() => {
    return sidebar?.agents ?? <AgentList />;
  }, [sidebar?.agents]);

  const conversations = useMemo(() => {
    return (
      sidebar?.conversations ?? (
        <Conversation
          height={height}
          extras={
            <Tooltip title="历史会话">
              <Button
                type="text"
                size="small"
                icon={<Icon icon="Search" />}
                onClick={() => navigate("/conversations")}
              />
            </Tooltip>
          }
        />
      )
    );
  }, [sidebar?.conversations, height]);

  const mainContent = useMemo(() => {
    return main ?? <div>main</div>;
  }, [main]);

  return (
    <DefaultAgentLayoutContext.Provider value={{ sideBarOpen }}>
      <div className="pts:flex pts:w-full pts:h-full">
        {/* Left sidebar - fixed width 280px */}
        {mode !== "mini" && (
          <div
            className="pts:flex pts:flex-col pts:bg-light-gray pts:border-gray-200 pts:border-r pts:h-full pts:overflow-hidden pts:transition-all pts:duration-300"
            style={{
              width: sideBarOpen ? "260px" : "0",
            }}
          >
            <div className="pts:w-[260px]">
              <LogoHeader />
              <div className="pts:flex-1 pts:overflow-y-auto">
                <div className="pts:pr-2 pts:pl-2">{agents}</div>
              </div>
              <div className="pts:mx-2 pts:border-gray-200 pts:border-t" />
              <div className="pts:pr-[2px]">{conversations}</div>
            </div>
          </div>
        )}

        {/* Middle content - adaptive width */}
        <div className="pts:flex-1 pts:h-full">{mainContent}</div>
      </div>
    </DefaultAgentLayoutContext.Provider>
  );
};

export default DefaultBasicLayout;
