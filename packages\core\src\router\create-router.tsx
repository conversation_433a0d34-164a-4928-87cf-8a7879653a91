import React, { useEffect, useState } from "react";
import { RouteObject, createBrowserRouter } from "react-router";

import { AgentChatHost } from "@/core";
import { getLoginUrl } from "@/core/common/vars";
import { get } from "@/request";
import { AgentChatConfig, ExtendRouterObject, StandardResponse } from "@/types";

export interface RouterConfig {
  pages?: {
    login?: {
      enable: boolean;
      path?: string;
      Component?: React.FC;
    };
    home?: {
      path?: string;
      Component?: React.FC;
    };
    chat?: {
      path?: string;
      Component?: React.FC;
    };
    agentHome?: {
      path?: string;
      Component?: React.FC;
    };
    conversationHistory?: {
      path?: string;
      Component?: React.FC;
    };
  };
  routes?: ExtendRouterObject[];
  rootRoutes?: ExtendRouterObject[];
  authGuard?: () => boolean;
  loginUrl?: string;
  basename?: string;
}

export interface CreateRouterOptions {
  basename?: string;
}

// eslint-disable-next-line react-refresh/only-export-components
const HostComponent: React.FC<{
  agentChatConfig: AgentChatConfig;
  Component?: React.FC;
}> = (props) => {
  const { agentChatConfig, Component, ...restProps } = props;
  const [config, setConfig] = useState<AgentChatConfig | null>(null);

  useEffect(() => {
    loadAndMergeConfigs(agentChatConfig).then((result) => {
      setConfig(result);
    });
  }, []);

  if (!Component) {
    console.error("missing Home Component");
    return null;
  }

  if (!config) {
    return null;
  }

  return (
    <AgentChatHost config={config}>
      <Component {...restProps} />
    </AgentChatHost>
  );
};

function loadAndMergeConfigs(config: AgentChatConfig) {
  return get<StandardResponse<any[]>>("/agent")
    .then((res) => {
      const agents = res.data.data;
      const localConfigs = config.agents;
      if (Array.isArray(agents)) {
        const newConfig = [];
        for (const i of agents) {
          const existed = localConfigs.find((c) => c.code === i.agent_code);
          if (existed) {
            newConfig.push({
              ...existed,
              agent_name: i.agent_name,
              logo: i.icon,
              description: i.agent_desc,
              welcome: i.welcome_statement,
            });
          }
        }
        config.agents = newConfig;
      }
      return config;
    })
    .catch(() => {
      return config;
    });
}

export async function createDefaultRouter(config: RouterConfig, agentChatConfig: AgentChatConfig) {
  const { pages = {}, routes = [], rootRoutes = [], authGuard, loginUrl, basename = "" } = config;

  const loader = () => {
    if (config.authGuard) {
      const isAuthed = authGuard ? authGuard() : true;
      if (!isAuthed) {
        location.href = loginUrl ?? getLoginUrl();
      }
    }
  };

  // 添加 auth 校验
  const $rootRoutes = rootRoutes.map((route) => {
    const { auth, ...restProps } = route;
    if (auth && !route.loader) {
      return {
        ...restProps,
        loader,
      };
    } else {
      return route;
    }
  });

  const $routes: RouteObject[] = [
    ...$rootRoutes,
    {
      path: "/",
      Component: () => {
        return <HostComponent agentChatConfig={agentChatConfig} Component={pages.home?.Component} />;
      },
      // 鉴权Guard
      loader,
      children: [
        {
          path: "chat/:id",
          Component: pages.chat?.Component,
        },
        {
          path: "agent/:code",
          Component: pages.agentHome?.Component,
        },
        {
          path: "conversations",
          Component: pages.conversationHistory?.Component,
        },
        ...routes,
      ],
    },
  ];

  if (pages.login?.enable) {
    const loginPageRoute: RouteObject = {
      path: "/login",
      Component: pages.login?.Component,
    };
    $routes.unshift(loginPageRoute);
  }

  const router = createBrowserRouter($routes, {
    basename,
  });

  return router;
}
