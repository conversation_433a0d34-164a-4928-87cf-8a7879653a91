import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import { PromptTemplate } from "./PromptTemplate";

const PromptCard: React.FC<{ data: PromptTemplate; actions?: React.ReactNode }> = (props) => {
  const { data, actions } = props;
  const runner = useCommandRunner();

  // 发送模板内容到聊天输入框
  const sendMessage = (message: string) => {
    runner(BuildInCommand.InsertTextIntoSender, {
      text: message,
    });
    runner(BuildInCommand.CloseSenderHeaderPanel);
  };

  return (
    <div
      className={` pts:transition-all ${data.deleted ? "pts:opacity-0" : "pts:opacity-100"}`}
      onClick={(e) => {
        e.stopPropagation();
        sendMessage(data.prompt);
      }}
    >
      <div className="pts:bg-white pts:p-3 pts:border pts:border-gray-200 pts:hover:border-blue-500 pts:rounded-sm pts:h-[84px] pts:cursor-pointer pts:select-none">
        <div className="pts:flex pts:items-start pts:mb-1 pts:font-medium">
          <span className="pts:bg-[rgba(108,144,242,0.10)] pts:px-[4px] pts:rounded-sm pts:text-[#6C90F2] pts:text-sm">
            <Icon icon="Template" />
          </span>
          <span className="pts:ml-2 pts:overflow-hidden pts:font-bold pts:text-black-85 pts:text-base pts:break-all pts:text-ellipsis pts:leading-[20px] pts:whitespace-nowrap">
            <Tooltip title={data.title}>{data.title}</Tooltip>
            <Button size="small" type="text">
              <Icon icon="Eye" />
            </Button>
          </span>
        </div>
        <div className="pts:flex pts:m-0 pts:mt-3">
          <p className="pts:flex-1 pts:overflow-hidden pts:text-black-65 pts:text-xs pts:text-ellipsis pts:leading-relaxed pts:whitespace-nowrap">
            <Tooltip title={data.description}>{data.description}</Tooltip>
          </p>
          <div className="pts:min-w-[52px] pts:max-w-[52px]">{actions}</div>
        </div>
      </div>
    </div>
  );
};

export default PromptCard;
