import { Button, Input, InputRef, Tooltip } from "antd";
import React, { useEffect } from "react";

import { Icon } from "@cscs-agent/icons";

import Highlight from "./Highlight";

interface ConversationTitleProps {
  title: string;
  keyword: string;
  onEdited: (title: string) => void;
  editable: boolean;
  onCancel: () => void;
}

const Title: React.FC<ConversationTitleProps> = (props) => {
  const { title, keyword, onEdited, onCancel, editable } = props;
  const [value, setValue] = React.useState(title);
  const inputRef = React.useRef<InputRef>(null);

  const confirmEdit = () => {
    onEdited(value);
  };

  const cancel = () => {
    setTimeout(() => {
      onCancel();
    }, 300);
  };

  useEffect(() => {
    if (editable) {
      setValue(title);
    }
  }, [editable, title]);

  useEffect(() => {
    if (editable) {
      inputRef.current?.focus();
    }
  }, [editable]);

  if (!editable) {
    return (
      <span className="pts:text-base">
        <Highlight text={title} keyword={keyword} />
      </span>
    );
  }

  return (
    <span
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <Input
        ref={inputRef}
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
        }}
        onBlur={cancel}
        suffix={
          <Tooltip title="确认修改">
            <Button
              type="text"
              size="small"
              onClick={(e) => {
                confirmEdit();
                e.stopPropagation();
              }}
              className="ag:translate-x-1.5 ag:transform"
            >
              <Icon icon="Save" />
            </Button>
          </Tooltip>
        }
      />
    </span>
  );
};

export default Title;
